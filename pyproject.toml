[project]
name = "deepwiki-open"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
  "fastapi>=0.95.0",
  "uvicorn>=0.21.1",
  "pydantic>=2.0.0",
  "google-generativeai>=0.3.0",
  "tiktoken>=0.5.0",
  "adalflow>=0.1.0",
  "numpy>=1.24.0",
  "faiss-cpu>=1.7.4",
  "langid>=1.1.6",
  "requests>=2.28.0",
  "jinja2>=3.1.2",
  "python-dotenv>=1.0.0",
  "openai>=1.76.2",
  "ollama>=0.4.8",
  "aiohttp>=3.8.4",
  "boto3>=1.34.0",
  "pytest>=7.0.0",
  "azure-identity>=1.12.0",
  "azure-core>=1.24.0"
]
