{"name": "deepwiki-open", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "dev:webpack": "next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"mermaid": "^11.4.1", "next": "^15.3.4", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "svg-pan-zoom": "^3.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}, "overrides": {"prismjs": ">=1.30.0", "highlight.js": ">=10.4.1"}}