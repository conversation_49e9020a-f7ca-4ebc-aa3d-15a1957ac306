#!/usr/bin/env python3
"""
Test script to verify Qwen embedding functionality
"""
import os
import sys
sys.path.append('.')

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Verify environment variables are loaded
print(f"✅ OPENAI_API_KEY loaded: {os.getenv('OPENAI_API_KEY', 'NOT_SET')[:15]}...")
print(f"✅ OPENAI_API_BASE_URL loaded: {os.getenv('OPENAI_API_BASE_URL', 'NOT_SET')}")

from api.rag import RAG

def test_qwen_embedding():
    print("🔄 Testing Qwen embedding with RAG...")
    
    try:
        # Initialize RAG with Qwen provider
        rag = RAG(provider="qwen", model="qwen-turbo")
        print("✅ RAG initialized successfully")
        
        # Test with a simple local directory
        test_repo = "."  # Current directory
        print(f"🔄 Preparing retriever for: {test_repo}")
        
        rag.prepare_retriever(
            repo_url_or_path=test_repo,
            type="local",
            access_token=None,
            excluded_dirs=[".git", "node_modules", "__pycache__", ".next"],
            excluded_files=["*.pyc", "*.log", "package-lock.json"],
            included_dirs=None,
            included_files=["*.py", "*.md", "*.json"]
        )
        print("✅ Retriever prepared successfully")
        
        # Test a simple query
        print("🔄 Testing query...")
        query = "What is this project about?"
        result = rag(query, language="en")
        
        if result and len(result) > 0 and hasattr(result[0], 'documents'):
            print(f"✅ Query successful! Retrieved {len(result[0].documents)} documents")
            for i, doc in enumerate(result[0].documents[:3]):  # Show first 3 docs
                file_path = doc.meta_data.get('file_path', 'unknown')
                content_preview = doc.text[:100] + "..." if len(doc.text) > 100 else doc.text
                print(f"  Doc {i+1}: {file_path} - {content_preview}")
        else:
            print("❌ Query returned no results")
            
        print("🎉 Qwen embedding test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error in Qwen embedding test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_qwen_embedding()
    sys.exit(0 if success else 1)
