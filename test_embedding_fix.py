#!/usr/bin/env python3
"""
Test script to verify embedding fix
"""
import os
import sys
sys.path.append('.')

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

def test_embedding_fix():
    print("🔄 Testing embedding fix...")
    
    try:
        from api.rag import RAG
        
        # Initialize RAG with Qwen provider (for embedding)
        rag = RAG(provider="qwen", model="qwen-turbo")
        print("✅ RAG initialized successfully")
        
        # Test embedding directly
        test_text = "This is a test text for embedding."
        print(f"🔄 Testing embedding with text: '{test_text}'")
        
        # Test the embedder directly
        result = rag.embedder(test_text)
        print(f"✅ Embedding successful")
        print(f"   Result type: {type(result)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Embedding test failed: {e}")
        return False

def test_google_config():
    print("\n🔄 Testing Google configuration...")
    
    try:
        import json
        
        # Load generator.json directly
        with open('api/config/generator.json', 'r') as f:
            config = json.load(f)
        
        google_config = config.get('providers', {}).get('google', {})
        default_model = google_config.get('default_model', 'NOT_SET')
        
        print(f"✅ Google provider config loaded")
        print(f"   Default model: {default_model}")
        
        # Check if the default model is available
        if default_model in ['gemini-1.5-pro', 'gemini-1.5-flash']:
            print(f"✅ Default model {default_model} should be available in your region")
            return True
        elif default_model == 'gemini-2.5-pro':
            print(f"⚠️  Default model {default_model} may have region restrictions")
            return False
        else:
            print(f"❓ Unknown default model: {default_model}")
            return False
            
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Embedding and Google Configuration Fixes\n")
    
    # Test embedding fix
    embedding_ok = test_embedding_fix()
    
    # Test Google config
    config_ok = test_google_config()
    
    print(f"\n📊 Test Results:")
    print(f"   Embedding Fix: {'✅ PASS' if embedding_ok else '❌ FAIL'}")
    print(f"   Google Config: {'✅ PASS' if config_ok else '❌ FAIL'}")
    
    if embedding_ok and config_ok:
        print(f"\n🎉 All fixes are working correctly!")
    else:
        print(f"\n⚠️  Some issues remain. Please check the errors above.")
